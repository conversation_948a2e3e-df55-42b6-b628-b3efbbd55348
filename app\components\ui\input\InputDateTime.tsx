import { forwardRef, Ref, useImperativeHandle, useRef, useState, useEffect, Fragment } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { cn } from "~/lib/utils";
import HintTooltip from "../tooltips/HintTooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../select";
import { Input } from "../input";

export interface RefInputDateTime {
  input: HTMLInputElement | null;
}

interface Props {
  format?: "12h" | "24h";
  name?: string;
  title: string;
  defaultValue?: Date | undefined;
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: string;
  icon?: string;
  darkMode?: boolean;
  autoFocus?: boolean;
  classNameInput?: string;
}

const InputDateTime = (
  {
    format = "24h",
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    darkMode,
    autoFocus,
    classNameInput,
  }: Props,
  ref: Ref<RefInputDateTime>
) => {
  const { t } = useTranslation();
  useImperativeHandle(ref, () => ({ input: input.current }));
  const input = useRef<HTMLInputElement>(null);

  const [actualValue, setActualValue] = useState<Date | undefined>(value || defaultValue);

  // State for 12h format dropdowns
  const [dateInput, setDateInput] = useState<string>("");
  const [hours, setHours] = useState<string>("12");
  const [minutes, setMinutes] = useState<string>("00");
  const [meridiem, setMeridiem] = useState<"AM" | "PM">("AM");

  // Update actualValue when value prop changes
  useEffect(() => {
    if (value !== actualValue) {
      // Ensure value is a valid Date object if provided
      if (value) {
        const dateValue = value instanceof Date ? value : new Date(value);
        if (!isNaN(dateValue.getTime())) {
          setActualValue(dateValue);
        }
      } else {
        setActualValue(value);
      }
    }
  }, [value]);

  // Initialize from defaultValue
  useEffect(() => {
    if (defaultValue && !actualValue) {
      // Ensure defaultValue is a valid Date object
      const dateValue = defaultValue instanceof Date ? defaultValue : new Date(defaultValue);
      if (!isNaN(dateValue.getTime())) {
        setActualValue(dateValue);
      }
    }
  }, [defaultValue]);

  // Update dropdown components when actualValue changes
  useEffect(() => {
    if (actualValue && format === "12h") {
      updateDropdownComponents(actualValue);
    }
  }, [actualValue, format]);

  const updateDropdownComponents = (date: Date) => {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return;

    // Update date input (YYYY-MM-DD format for date input)
    const year = dateObj.getFullYear();
    const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
    const day = dateObj.getDate().toString().padStart(2, "0");
    setDateInput(`${year}-${month}-${day}`);

    // Update time components for 12h format
    let hour12 = dateObj.getHours();
    const ampm = hour12 >= 12 ? "PM" : "AM";
    hour12 = hour12 % 12;
    if (hour12 === 0) hour12 = 12;

    setHours(hour12.toString().padStart(2, "0"));
    setMinutes(dateObj.getMinutes().toString().padStart(2, "0"));
    setMeridiem(ampm);
  };

  const handleDropdownChange = (newDate: string, newHour: string, newMinute: string, newMeridiem: "AM" | "PM") => {
    if (!newDate) return;

    const [year, month, day] = newDate.split("-");
    let hour = parseInt(newHour);
    const minute = parseInt(newMinute);

    // Convert 12h to 24h format
    if (newMeridiem === "PM" && hour !== 12) hour += 12;
    if (newMeridiem === "AM" && hour === 12) hour = 0;

    const newDateTime = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), hour, minute);
    setActualValue(newDateTime);

    if (onChange) {
      onChange(newDateTime);
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value;
    setDateInput(newDate);
    handleDropdownChange(newDate, hours, minutes, meridiem);
  };

  const handleHourChange = (newHour: string) => {
    setHours(newHour);
    handleDropdownChange(dateInput, newHour, minutes, meridiem);
  };

  const handleMinuteChange = (newMinute: string) => {
    setMinutes(newMinute);
    handleDropdownChange(dateInput, hours, newMinute, meridiem);
  };

  const handleMeridiemChange = (newMeridiem: "AM" | "PM") => {
    setMeridiem(newMeridiem);
    handleDropdownChange(dateInput, hours, minutes, newMeridiem);
  };

  const getDateTimeInputValue = (): string => {
    if (!actualValue) return "";

    // Ensure actualValue is a Date object
    const dateValue = actualValue instanceof Date ? actualValue : new Date(actualValue);
    if (isNaN(dateValue.getTime())) return "";

    // Format for datetime-local input: YYYY-MM-DDTHH:mm
    const year = dateValue.getFullYear();
    const month = (dateValue.getMonth() + 1).toString().padStart(2, "0");
    const day = dateValue.getDate().toString().padStart(2, "0");
    const hours = dateValue.getHours().toString().padStart(2, "0");
    const minutes = dateValue.getMinutes().toString().padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const handleDateTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateTimeValue = e.target.value;
    if (dateTimeValue) {
      const date = new Date(dateTimeValue);
      if (!isNaN(date.getTime())) {
        setActualValue(date);
        if (onChange) {
          onChange(date);
        }
      }
    } else {
      setActualValue(undefined);
      if (onChange) {
        onChange(undefined);
      }
    }
  };

  const formatDisplayValue = (date: Date): string => {
    // Ensure date is a Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return "";

    if (format === "12h") {
      // DD/MM/YYYY hh:mm A format
      const day = dateObj.getDate().toString().padStart(2, "0");
      const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
      const year = dateObj.getFullYear();

      // Format time manually to ensure consistent format
      let hours = dateObj.getHours();
      const minutes = dateObj.getMinutes().toString().padStart(2, "0");
      const ampm = hours >= 12 ? "PM" : "AM";
      hours = hours % 12;
      if (hours === 0) hours = 12;
      const formattedHours = hours.toString().padStart(2, "0");

      return `${day}/${month}/${year} ${formattedHours}:${minutes} ${ampm}`;
    } else {
      // DD/MM/YYYY HH:mm format (24-hour)
      const day = dateObj.getDate().toString().padStart(2, "0");
      const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
      const year = dateObj.getFullYear();
      const hours = dateObj.getHours().toString().padStart(2, "0");
      const minutes = dateObj.getMinutes().toString().padStart(2, "0");
      return `${day}/${month}/${year} ${hours}:${minutes}`;
    }
  };

  return (
    <div className={clsx(className, !darkMode && "text-foreground")}>
      <div className="flex justify-between space-x-2">
        <label htmlFor={name} className="block text-xs font-medium">
          {title}
          {required && <span className="ml-1 text-red-500">*</span>}
        </label>
        <div className="mt-1">
          {hint && <HintTooltip text={hint} />}
        </div>
      </div>

      <div className="relative mt-1">
        {icon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg className="text-muted-foreground h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
{format === "24h" ? (
  <div className="relative">
    <Input
      ref={input}
      type="datetime-local"
      id={name}
      name={name}
      required={required}
      value={getDateTimeInputValue()}
      onChange={handleDateTimeInputChange}
      disabled={disabled || readOnly}
      readOnly={readOnly}
      autoFocus={autoFocus}
      className={cn(
        "rounded-md pr-10", // Add right padding for icon
        (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed",
        classNameInput
      )}
    />
    {/* Clock Icon on the Right */}
    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-4 w-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    </div>
  </div>
) : (


          <Fragment>
            {name && (
              <input
                type="hidden"
                name={name}
                value={getDateTimeInputValue()}
                required={required}
                disabled={actualValue === undefined}
                hidden
                readOnly
                autoFocus={autoFocus}
                className={cn(
                  "rounded-md",
                  (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed",
                  icon && "pl-10",
                  classNameInput
                )}
              />
            )}
            {/* Single unified component */}
            <div className={cn(
              "flex items-center w-full rounded-lg border border-border/60 px-4 py-2.5 h-10 text-sm",
              (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed opacity-50",
              icon && "pl-10",
              classNameInput
            )}>
              {/* Date Section */}
              <div className="flex-1 min-w-0 max-w-[50%]">
                <input
                  type="date"
                  value={dateInput}
                  onChange={handleDateChange}
                  disabled={disabled || readOnly}
                  readOnly={readOnly}
                  className="w-full bg-transparent border-0 outline-none focus:outline-none text-sm text-foreground placeholder:text-muted-foreground"
                  style={{ colorScheme: 'light' }}
                />
              </div>
              {/* Separator */}
              <div className="flex items-center px-3">
                <div className="h-6 w-px bg-gradient-to-b from-transparent via-border to-transparent"></div>
              </div>

              {/* Time Section */}
              <div className="flex items-center space-x-1.5 flex-shrink-0">
                {/* Hour Dropdown */}
                <Select value={hours} onValueChange={handleHourChange} disabled={disabled || readOnly}>
                  <SelectTrigger className="border-0 shadow-none p-1 h-auto w-12 bg-transparent text-sm hover:bg-accent/50 rounded transition-colors">
                    <SelectValue placeholder="12" />
                  </SelectTrigger>
                  <SelectContent align="end">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((h) => (
                      <SelectItem key={h} value={h.toString().padStart(2, "0")}>
                        {h.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Colon */}
                <span className="text-muted-foreground/70 text-sm font-medium">:</span>

                {/* Minute Dropdown */}
                <Select value={minutes} onValueChange={handleMinuteChange} disabled={disabled || readOnly}>
                  <SelectTrigger className="border-0 shadow-none p-1 h-auto w-12 bg-transparent text-sm hover:bg-accent/50 rounded transition-colors">
                    <SelectValue placeholder="00" />
                  </SelectTrigger>
                  <SelectContent align="end">
                    {Array.from({ length: 60 }, (_, i) => i).map((m) => (
                      <SelectItem key={m} value={m.toString().padStart(2, "0")}>
                        {m.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* AM/PM Dropdown */}
                <Select value={meridiem} onValueChange={handleMeridiemChange} disabled={disabled || readOnly}>
                  <SelectTrigger className="border-0 shadow-none p-1 h-auto w-14 bg-transparent text-sm hover:bg-accent/50 rounded transition-colors">
                    <SelectValue placeholder="AM" />
                  </SelectTrigger>
                  <SelectContent align="end">
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Clock Icon - Positioned at far right */}
              <div className="ml-auto flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
            </div>
          </Fragment>
        )}

        {actualValue && (
          <div className="mt-2 px-1">
            <div className="text-xs text-muted-foreground/80 font-medium">
              {t("dateTime.preview")}: <span className="text-foreground/90">{formatDisplayValue(actualValue)}</span>
            </div>
          </div>
        )}
      </div>

      {help && <div className="mt-2 text-xs text-muted-foreground">{help}</div>}
    </div>
  );
};

export default forwardRef(InputDateTime);
