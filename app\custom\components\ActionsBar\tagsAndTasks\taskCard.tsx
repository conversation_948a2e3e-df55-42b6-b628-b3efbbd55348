"use client";

import { useSubmit } from "react-router";
import { formatDistanceToNow } from "date-fns";
import { useEffect, useRef, useState } from "react";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { RowTaskWithDetails } from "~/utils/db/entities/rowTasks.db.server";
import { create } from "handlebars";
import { createPortal } from "react-dom";

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  message: string;
  confirmMessage: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ isOpen, onClose, onConfirm, message, confirmMessage }) => {
  if (!isOpen) return null;
  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 ">
      <div className="w-80 rounded-lg bg-white p-6 shadow-lg">
        <div>
          <div className="flex gap-2 mb-4">
            <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M10.0007 3.40825L16.2757 14.2499H3.72565L10.0007 3.40825ZM10.0007 0.083252L0.833984 15.9166H19.1673L10.0007 0.083252ZM10.834 11.7499H9.16732V13.4166H10.834V11.7499ZM10.834 6.74992H9.16732V10.0833H10.834V6.74992Z"
                fill="#274AFF"
              />
            </svg>
            <div className="text-base font-semibold text-primary">Delete Task</div>
          </div>
          <p className="mb-4 text-base text-gray-800">{message}</p>
        </div>
        <div className="flex justify-end gap-3">
          <button className="rounded-lg border border-[#D9D9D9] bg-white px-3 py-1 text-sm cursor-pointer" onClick={onClose}>
            Cancel
          </button>
          <button className="rounded-lg bg-primary px-3 py-1 text-sm text-white cursor-pointer" onClick={onConfirm}>
            {confirmMessage}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

interface TaskHeaderProps {
  item: any;
  onConfirmAction: any;
  title: string;
  author: string;
  date: string;
  canDelete: any;
  onDeleteTask: any;
  onEdit: () => void;
  onMarkDone: any;
}

const TaskBadge = ({ status, variant }: any) => {
  const variants: any = {
    Pending: "text-[#FF7800] bg-[#FFF0E5]",
    Done: "text-[#0A0501] bg-[#E6E6E6]",
  };

  return (
    <div className={`flex items-start whitespace-nowrap text-center text-xs font-medium leading-normal bg-blend-multiply`}>
      <div className={`self-stretch rounded-[4px] px-1.5 py-[1px]  font-medium ${variants[variant]}`}>{status == "Completed" ? "Done" : status}</div>
    </div>
  );
};

const TaskHeader = ({ item, title, author, date, onDeleteTask, canDelete, onConfirmAction, onEdit, onMarkDone }: TaskHeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    < div className="flex w-full items-start relative">
      <div className="w-[90%] flex-1 shrink basis-0">
        <div>
          <h3 className="w-full truncate text-foreground font-inter text-[12px] not-italic font-semibold leading-[16px]">{title}</h3>
        </div>
        <div className="pt-[2.91px] flex items-baseline">
          <p className="text-xs text-muted-foreground text-[10.17px] font-normal">
            Added by <span className="inline-block truncate align-bottom">{author}</span> ({date})
          </p>
        </div>
      </div>

      <div ref={menuRef}>
        <button className="rounded-[5px] p-1 focus:outline-none border-[1px] border-input cursor-pointer" onClick={() => setIsMenuOpen(!isMenuOpen)}>
          <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 15 15" fill="none">
            <path d="M7.51042 11.7826C7.18958 11.7826 6.91493 11.6683 6.68646 11.4398C6.45799 11.2114 6.34375 10.9367 6.34375 10.6159C6.34375 10.2951 6.45799 10.0204 6.68646 9.79193C6.91493 9.56346 7.18958 9.44922 7.51042 9.44922C7.83125 9.44922 8.1059 9.56346 8.33438 9.79193C8.56285 10.0204 8.67708 10.2951 8.67708 10.6159C8.67708 10.9367 8.56285 11.2114 8.33438 11.4398C8.1059 11.6683 7.83125 11.7826 7.51042 11.7826ZM7.51042 8.28255C7.18958 8.28255 6.91493 8.16832 6.68646 7.93984C6.45799 7.71137 6.34375 7.43672 6.34375 7.11589C6.34375 6.79505 6.45799 6.5204 6.68646 6.29193C6.91493 6.06346 7.18958 5.94922 7.51042 5.94922C7.83125 5.94922 8.1059 6.06346 8.33438 6.29193C8.56285 6.5204 8.67708 6.79505 8.67708 7.11589C8.67708 7.43672 8.56285 7.71137 8.33438 7.93984C8.1059 8.16832 7.83125 8.28255 7.51042 8.28255ZM7.51042 4.78255C7.18958 4.78255 6.91493 4.66832 6.68646 4.43984C6.45799 4.21137 6.34375 3.93672 6.34375 3.61589C6.34375 3.29505 6.45799 3.0204 6.68646 2.79193C6.91493 2.56345 7.18958 2.44922 7.51042 2.44922C7.83125 2.44922 8.1059 2.56345 8.33438 2.79193C8.56285 3.0204 8.67708 3.29505 8.67708 3.61589C8.67708 3.93672 8.56285 4.21137 8.33438 4.43984C8.1059 4.66832 7.83125 4.78255 7.51042 4.78255Z" fill="#202229" />
          </svg>
        </button>

        {isMenuOpen && (
          <div className="absolute right-0 z-80 w-40 rounded-md border border-gray-200 bg-white shadow-md">
            <ul className="text-sm text-gray-700">
              <li
                className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                onClick={() => {
                  onMarkDone(item.id);
                  setIsMenuOpen(false);
                }}
              >
                {item.completed ? "Mark as Pending" : "Mark as Done"}
              </li>
              <li
                className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                onClick={() => {
                  onEdit();
                  setIsMenuOpen(false);
                }}
              >
                Edit
              </li>
              <li
                className="cursor-pointer px-4 py-2 text-primary hover:bg-gray-100"
                onClick={() => {
                  if (canDelete(item)) {
                    onConfirmAction(() => onDeleteTask(item.id), "Are you sure you want to delete the selected task?", "Yes, Delete");
                  }
                  setIsMenuOpen(false);
                }}
              >
                Delete
              </li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

const PriorityBadge = ({ status = "Low", variant = "Low" }: any) => {
  console.log("PriorityBadge rendered", variant);
  const variants: any = {
    Medium: "text-[#FF7800] bg-[#FFF0E5]",
    High: "text-[#FE7070] bg-[#FFE1E1]",
    Low: "text-[#23D269] bg-[#E9FBF0]",
    low: "text-[#23D269] bg-[#E9FBF0]",
  };

  return (
    <div className={`flex items-start whitespace-nowrap text-foreground text-center text-xs font-medium leading-normal bg-blend-multiply`}>
      <div className={` self-stretch rounded-[4px]  px-1.5 py-px font-medium ${variants[variant]}`}>
        {" "}
        Priority: <span className="font-bold">{status}</span>
      </div>
    </div>
  );
};

const TaskFooter = ({ timeAgo, priority, status }: any) => {
  return (
    <div className="mt-3 flex w-full items-start gap-[4px]">
      <div className="flex flex-1 shrink basis-0 items-start justify-between text-xs  text-muted-foreground font-inter text-[10.166px] not-italic font-normal leading-[14.522px]">
        <p className="w-full flex-1 shrink">{timeAgo}</p>
      </div>
      <TaskBadge status={status} variant={status} />
      <PriorityBadge status={priority} variant={priority} />
    </div>
  );
};

const TaskCard = ({ item, status, open, setIsOpen, setTaskEdit }: any) => {
  const appOrAdminData = useAppOrAdminData();
  const submit = useSubmit();
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    message: string;
    confirmMessage: string;
    action: (() => void) | null;
  }>({
    isOpen: false,
    message: "",
    confirmMessage: "",
    action: null,
  });

  function onConfirmAction(action: () => void, message: string, confirmMessage: string) {
    setModalState({
      isOpen: true,
      message,
      confirmMessage,
      action,
    });
  }

  function onToggleTaskCompleted(id: string) {
    const form = new FormData();
    form.set("action", "task-complete-toggle");
    form.set("task-id", id);
    submit(form, {
      method: "post",
    });
  }

  function onDeleteTask(id: string) {
    const form = new FormData();
    form.set("action", "task-delete");
    form.set("task-id", id);
    submit(form, {
      method: "post",
    });
  }

  function canDelete(item: RowTaskWithDetails) {
    return appOrAdminData.isSuperUser || (!item.completed && item.createdByUserId === appOrAdminData.user?.id);
  }

  const handleEditOpen = () => {
    setTaskEdit(item);
    setIsOpen(true);
  };

  const createdAt = new Date(item.createdAt);
  const date = createdAt
    .toLocaleString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    })
    .replace(",", " |");
  const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });
  const formattedTimeAgo = timeAgo
    .replace("about", "")
    .replace("minutes", "mins")
    .replace("minute", "min")
    .replace("hours", "hrs")
    .replace("hour", "hr")
    .replace("days", "day")
    .replace("day", "day")
    .replace("months", "months")
    .replace("month", "month");
  console.log("TaskCard rendered", item, status, open);
  return (
    <>
      <article
        className={` rounded-[8px]  border border-input bg-card shadow-[0px_0.726px_1.452px_0px_rgba(16,24,40,0.05)] p-3 `}
      >
        <div className="flex w-full flex-col">
          <TaskHeader
            item={item}
            title={item.title}
            author={appOrAdminData.user.firstName}
            date={date}
            canDelete={canDelete}
            onDeleteTask={onDeleteTask}
            onEdit={() => handleEditOpen()}
            onConfirmAction={onConfirmAction}
            onMarkDone={onToggleTaskCompleted}
          />

        </div>
        <div className="mt-3 flex min-h-0 w-full border-b border-zinc-100" />
        <TaskFooter timeAgo={formattedTimeAgo} priority={item.priority} status={item?.completed ? "Done" : "Pending"} />
      </article>
      <ConfirmationModal
        isOpen={modalState.isOpen}
        onClose={() => setModalState((prev) => ({ ...prev, isOpen: false }))}
        onConfirm={() => {
          modalState.action?.();
          setModalState((prev) => ({ ...prev, isOpen: false }));
        }}
        message={modalState.message}
        confirmMessage={modalState.confirmMessage}
      />
    </>
  );
};

export default TaskCard;
