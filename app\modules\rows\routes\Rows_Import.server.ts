
import { LoaderFunctionArgs, redirect, ActionFunction } from "react-router";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { Colors } from "~/application/enums/shared/Colors";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import UrlUtils from "~/utils/app/UrlUtils";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { getMaxRowFolio, getRowById, RowWithDetails } from "~/utils/db/entities/rows.db.server";
import RowHelper from "~/utils/helpers/RowHelper";
import RowsRequestUtils from "../utils/RowsRequestUtils";
import { adminGetAllTenantsIdsAndNames, getTenant } from "~/utils/db/tenants.db.server";
import { db } from "~/utils/db.server";

// Helper function to normalize field names for flexible matching
function normalizeFieldName(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9]/g, '');
}

// Helper function to check if a field name matches a relationship pattern
function isRelationshipField(fieldName: string, entityName: string): { isMatch: boolean; type: 'id' | 'name' | null } {
  const normalizedField = normalizeFieldName(fieldName);
  const normalizedEntity = normalizeFieldName(entityName);

  // Check for ID patterns: categoryId, category_id, category-id, etc.
  if (normalizedField === `${normalizedEntity}id` ||
      normalizedField === `${normalizedEntity}_id` ||
      normalizedField === `${normalizedEntity}-id` ||
      normalizedField.endsWith('id') && normalizedField.startsWith(normalizedEntity)) {
    return { isMatch: true, type: 'id' };
  }

  // Check for name patterns: categoryName, category_name, category-name, etc.
  if (normalizedField === `${normalizedEntity}name` ||
      normalizedField === `${normalizedEntity}_name` ||
      normalizedField === `${normalizedEntity}-name` ||
      normalizedField.endsWith('name') && normalizedField.startsWith(normalizedEntity)) {
    return { isMatch: true, type: 'name' };
  }

  return { isMatch: false, type: null };
}

// Helper function to find related row by business ID (prioritized approach)
async function findRelatedRowById(
  entityId: string,
  tenantId: string | null,
  businessId: string
): Promise<any> {
  const trimmedId = businessId.trim();
  if (!trimmedId) return null;

  console.log(`🔍 Searching for related row by business ID: entityId=${entityId}, businessId="${trimmedId}"`);

  try {
    // Strategy 1: Look for exact match in any text field (most reliable for IDs)
    let relatedRow = await db.row.findFirst({
      where: {
        entityId: entityId,
        tenantId: tenantId,
        values: {
          some: {
            textValue: trimmedId,
          },
        },
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    if (relatedRow) {
      console.log(`✅ Found exact business ID match: ${relatedRow.id}`);
      return relatedRow;
    }

    // Strategy 2: Case-insensitive exact match
    relatedRow = await db.row.findFirst({
      where: {
        entityId: entityId,
        tenantId: tenantId,
        values: {
          some: {
            textValue: {
              equals: trimmedId,
              mode: 'insensitive',
            },
          },
        },
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    if (relatedRow) {
      console.log(`✅ Found case-insensitive business ID match: ${relatedRow.id}`);
      return relatedRow;
    }

    console.warn(`❌ No business ID match found for: "${trimmedId}"`);
    return null;

  } catch (error) {
    console.error(`❌ Error searching for related row by business ID:`, error);
    return null;
  }
}

// Helper function to find related row by name (fallback approach)
async function findRelatedRowByName(
  entityId: string,
  tenantId: string | null,
  name: string
): Promise<any> {
  const trimmedName = name.trim();
  if (!trimmedName) return null;

  console.log(`🔍 Searching for related row by name: entityId=${entityId}, name="${trimmedName}"`);

  try {
    // Strategy 1: Search in display fields first (most likely to contain names)
    let relatedRow = await db.row.findFirst({
      where: {
        entityId: entityId,
        tenantId: tenantId,
        values: {
          some: {
            property: {
              isDisplay: true,
            },
            textValue: {
              equals: trimmedName,
              mode: 'insensitive',
            },
          },
        },
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    if (relatedRow) {
      console.log(`✅ Found name match in display field: ${relatedRow.id}`);
      return relatedRow;
    }

    // Strategy 2: Search in any text field
    relatedRow = await db.row.findFirst({
      where: {
        entityId: entityId,
        tenantId: tenantId,
        values: {
          some: {
            textValue: {
              equals: trimmedName,
              mode: 'insensitive',
            },
          },
        },
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    if (relatedRow) {
      console.log(`✅ Found name match in text field: ${relatedRow.id}`);
      return relatedRow;
    }

    console.warn(`❌ No name match found for: "${trimmedName}"`);
    return null;

  } catch (error) {
    console.error(`❌ Error searching for related row by name:`, error);
    return null;
  }
}

// Helper function to process relationship data
async function processRelationshipData(
  entity: EntityWithDetails,
  properties: { name: string; value: string }[],
  tenantId: string | null
): Promise<{
  regularProperties: { name: string; value: string }[];
  parentRows: { relationshipId: string; parentId: string }[];
  childRows: { relationshipId: string; childId: string }[];
}> {
  const regularProperties: { name: string; value: string }[] = [];
  const parentRows: { relationshipId: string; parentId: string }[] = [];
  const childRows: { relationshipId: string; childId: string }[] = [];

  console.log(`\n=== Processing relationship data for entity: ${entity.name} ===`);
  console.log(`Parent relationships: ${entity.parentEntities.length}`);
  console.log(`Child relationships: ${entity.childEntities.length}`);
  console.log(`Properties to process:`, properties.map(p => `${p.name}=${p.value}`));

  for (const property of properties) {
    const { name, value } = property;
    let isRelationshipFieldFound = false;

    console.log(`\nProcessing property: ${name}=${value}`);

    // Check parent relationships (many-to-one)
    for (const relationship of entity.parentEntities) {
      const relatedEntityName = relationship.parent.name;
      const fieldMatch = isRelationshipField(name, relatedEntityName);

      console.log(`Checking parent relationship: ${relatedEntityName}, fieldMatch:`, fieldMatch);

      if (fieldMatch.isMatch && fieldMatch.type) {
        console.log(`Found matching relationship field: ${name} -> ${relatedEntityName} (${fieldMatch.type})`);

        if (value && value.trim()) {
          let relatedRow = null;

          // Prioritize ID-based matching for more accuracy
          if (fieldMatch.type === 'id') {
            relatedRow = await findRelatedRowById(
              relationship.parent.id,
              tenantId,
              value
            );
          } else {
            // Fallback to name-based matching
            relatedRow = await findRelatedRowByName(
              relationship.parent.id,
              tenantId,
              value
            );
          }

          if (relatedRow) {
            parentRows.push({
              relationshipId: relationship.id,
              parentId: relatedRow.id,
            });
            console.log(`✅ Created parent relationship: ${name}=${value} -> ${relatedEntityName} row ID: ${relatedRow.id}`);
          } else {
            console.warn(`❌ Could not find related ${relatedEntityName} with ${fieldMatch.type}: ${value}`);
          }
        }
        isRelationshipFieldFound = true;
        break;
      }
    }

    // Check child relationships (one-to-many) if not already processed
    if (!isRelationshipFieldFound) {
      for (const relationship of entity.childEntities) {
        const relatedEntityName = relationship.child.name;
        const fieldMatch = isRelationshipField(name, relatedEntityName);

        console.log(`Checking child relationship: ${relatedEntityName}, fieldMatch:`, fieldMatch);

        if (fieldMatch.isMatch && fieldMatch.type) {
          console.log(`Found matching child relationship field: ${name} -> ${relatedEntityName} (${fieldMatch.type})`);

          if (value && value.trim()) {
            let relatedRow = null;

            // Prioritize ID-based matching for more accuracy
            if (fieldMatch.type === 'id') {
              relatedRow = await findRelatedRowById(
                relationship.child.id,
                tenantId,
                value
              );
            } else {
              // Fallback to name-based matching
              relatedRow = await findRelatedRowByName(
                relationship.child.id,
                tenantId,
                value
              );
            }

            if (relatedRow) {
              childRows.push({
                relationshipId: relationship.id,
                childId: relatedRow.id,
              });
              console.log(`✅ Created child relationship: ${name}=${value} -> ${relatedEntityName} row ID: ${relatedRow.id}`);
            } else {
              console.warn(`❌ Could not find related ${relatedEntityName} with ${fieldMatch.type}: ${value}`);
            }
          }
          isRelationshipFieldFound = true;
          break;
        }
      }
    }

    // If not a relationship field, add to regular properties
    if (!isRelationshipFieldFound) {
      console.log(`Adding as regular property: ${name}=${value}`);
      regularProperties.push(property);
    }
  }

  console.log(`\n=== Relationship processing complete ===`);
  console.log(`Regular properties: ${regularProperties.length}`);
  console.log(`Parent relationships: ${parentRows.length}`);
  console.log(`Child relationships: ${childRows.length}`);
  console.log(`Parent relationship IDs: ${parentRows.map(p => p.parentId).join(', ')}`);
  console.log(`Child relationship IDs: ${childRows.map(c => c.childId).join(', ')}`);
  console.log(`=======================================\n`);

  return {
    regularProperties,
    parentRows,
    childRows,
  };
}

export namespace Rows_Import {
  export type LoaderData = {
    meta: MetaTagsDto;
    entity: EntityWithDetails;
    allTenants: { id: string; name: string; slug: string }[];
  };
  export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    const { t, tenantId, entity } = await RowsRequestUtils.getLoader({ request, params });
    if (!entity.isAutogenerated || entity.type === "system") {
      throw redirect(tenantId ? UrlUtils.currentTenantUrl(params, "404") : "/404");
    }
    const data: LoaderData = {
      meta: [{ title: `${t("shared.import")} ${t(entity.titlePlural)} | ${process.env.APP_NAME}` }],
      entity,
      allTenants: !tenantId ? await adminGetAllTenantsIdsAndNames() : [],
    };
    return Response.json(data);
  };

  export interface ImportRow {
    properties: { name: string; value: string }[];
    row?: RowWithDetails | null;
    error?: string;
  }
  export type ActionData = {
    rows?: ImportRow[];
    error?: string;
  };
  export const action: ActionFunction = async ({ request, params }) => {
    const { t, userId, tenantId, entity, form } = await RowsRequestUtils.getAction({ request, params });
    const action = form.get("action");
    if (action === "import") {
      const tag = form.get("tag")?.toString() ?? "import";
      const rawRows: ImportRow[] = form.getAll("rows[]").map((f: FormDataEntryValue) => {
        return JSON.parse(f.toString());
      });
      let tenantToImport = tenantId;
      if (tenantId === null) {
        const selectedTenantId = form.get("selectedTenantId")?.toString() || "{null}";
        if (selectedTenantId === "{null}") {
          tenantToImport = null;
        } else {
          const existingTenant = await getTenant(selectedTenantId);
          if (!existingTenant) {
            return Response.json({ error: "Invalid tenant with ID: " + selectedTenantId }, { status: 400 });
          }
          tenantToImport = selectedTenantId;
        }
      }

      if (rawRows.length === 0) {
        return Response.json({ error: "No rows to import" }, { status: 400 });
      }
      const rows: ImportRow[] = [];
      let folio = 1;
      const maxFolio = await getMaxRowFolio({ tenantId: tenantToImport, entityId: entity.id });
      if (maxFolio && maxFolio._max.folio !== null) {
        folio = maxFolio._max.folio + 1;
      }
      await Promise.all(
        rawRows.map(async (importRow: ImportRow, idx) => {
          try {
            // Wait for keeping folios unique, I can't think of another way to do this
            await new Promise((r) => setTimeout(r, 1500));

            // Process relationship data
            const { regularProperties, parentRows, childRows } = await processRelationshipData(
              entity,
              importRow.properties,
              tenantToImport
            );

            // Debug logging
            console.log(`Processing row ${idx + 1}:`, {
              originalProperties: importRow.properties.length,
              regularProperties: regularProperties.length,
              parentRows: parentRows.length,
              childRows: childRows.length,
              parentRowIds: parentRows.map(p => p.parentId),
              childRowIds: childRows.map(c => c.childId),
            });

            // Get row values from regular properties only
            const rowValues = RowHelper.getRowPropertiesFromForm({
              t,
              entity,
              values: regularProperties
            });

            // Add relationship data to row values
            if (parentRows.length > 0 || childRows.length > 0) {
              rowValues.parentRows = parentRows;
              rowValues.childRows = childRows;
            }

            const newRow = await RowsApi.create({
              entity,
              tenantId: tenantToImport,
              userId,
              rowValues,
              nextFolio: folio + idx,
              request,
            });
            if (tag) {
              await RowsApi.addTag({ row: newRow, tag: { value: tag, color: Colors.INDIGO } });
            }
            importRow.row = await getRowById(newRow.id);
          } catch (e: any) {
            importRow.error = e.message?.toString();
          }
          rows.push(importRow);
        })
      );
      const data: ActionData = {
        rows,
      };
      return data;
    } else {
      return Response.json({ error: "Invalid form" }, { status: 400 });
    }
  };
}
