
import { LoaderFunctionArgs, redirect, ActionFunction } from "react-router";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { Colors } from "~/application/enums/shared/Colors";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import UrlUtils from "~/utils/app/UrlUtils";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { getMaxRowFolio, getRowById, RowWithDetails } from "~/utils/db/entities/rows.db.server";
import RowHelper from "~/utils/helpers/RowHelper";
import RowsRequestUtils from "../utils/RowsRequestUtils";
import { adminGetAllTenantsIdsAndNames, getTenant } from "~/utils/db/tenants.db.server";
import { db } from "~/utils/db.server";

// Helper function to normalize field names for flexible matching
function normalizeFieldName(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9]/g, '');
}

// Helper function to check if a field name matches a relationship pattern
function isRelationshipField(fieldName: string, entityName: string): { isMatch: boolean; type: 'id' | 'name' | null } {
  const normalizedField = normalizeFieldName(fieldName);
  const normalizedEntity = normalizeFieldName(entityName);

  console.log(`Checking field match: "${fieldName}" (${normalizedField}) vs entity "${entityName}" (${normalizedEntity})`);

  // Check for ID patterns: categoryId, category_id, category-id, categoryid, etc.
  const idPatterns = [
    `${normalizedEntity}id`,
    `${normalizedEntity}_id`,
    `${normalizedEntity}-id`,
    `${normalizedEntity}code`,
    `${normalizedEntity}_code`,
    `${normalizedEntity}-code`
  ];

  for (const pattern of idPatterns) {
    if (normalizedField === pattern) {
      console.log(`✅ Found ID pattern match: ${fieldName} -> ${pattern}`);
      return { isMatch: true, type: 'id' };
    }
  }

  // Check for name patterns: categoryName, category_name, category-name, etc.
  const namePatterns = [
    `${normalizedEntity}name`,
    `${normalizedEntity}_name`,
    `${normalizedEntity}-name`,
    `${normalizedEntity}title`,
    `${normalizedEntity}_title`,
    `${normalizedEntity}-title`
  ];

  for (const pattern of namePatterns) {
    if (normalizedField === pattern) {
      console.log(`✅ Found name pattern match: ${fieldName} -> ${pattern}`);
      return { isMatch: true, type: 'name' };
    }
  }

  // Also check if field ends with 'id' or 'name' and starts with entity name
  if (normalizedField.startsWith(normalizedEntity)) {
    if (normalizedField.endsWith('id') || normalizedField.endsWith('code')) {
      console.log(`✅ Found flexible ID match: ${fieldName} starts with ${entityName} and ends with id/code`);
      return { isMatch: true, type: 'id' };
    }
    if (normalizedField.endsWith('name') || normalizedField.endsWith('title')) {
      console.log(`✅ Found flexible name match: ${fieldName} starts with ${entityName} and ends with name/title`);
      return { isMatch: true, type: 'name' };
    }
  }

  console.log(`❌ No pattern match found for: ${fieldName} vs ${entityName}`);
  return { isMatch: false, type: null };
}

// Helper function to normalize text for flexible matching
function normalizeText(text: string): string {
  if (!text) return '';
  // Remove quotes, trim whitespace, convert to lowercase
  return text.replace(/['"]/g, '').trim().toLowerCase();
}

// Helper function to find related row by specific property (for Category)
async function findCategoryByBusinessId(
  entityId: string,
  tenantId: string | null,
  businessId: string
): Promise<any> {
  const originalValue = businessId.trim();
  const normalizedValue = normalizeText(businessId);

  if (!originalValue) return null;

  console.log(`🔍 Searching for Category by business ID: "${originalValue}"`);

  try {
    // Search specifically in the categoryid property
    const categoryRow = await db.row.findFirst({
      where: {
        entityId: entityId,
        tenantId: tenantId,
        values: {
          some: {
            property: {
              name: 'categoryid', // Hardcoded to search in categoryid property
            },
            textValue: originalValue,
          },
        },
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    if (categoryRow) {
      console.log(`✅ Found Category by categoryid: "${originalValue}" -> row ID: ${categoryRow.id}`);
      return categoryRow;
    }

    // Try normalized search if exact match fails
    const categoryRowNormalized = await db.row.findFirst({
      where: {
        entityId: entityId,
        tenantId: tenantId,
        values: {
          some: {
            property: {
              name: 'categoryid',
            },
            textValue: {
              equals: normalizedValue,
              mode: 'insensitive',
            },
          },
        },
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    if (categoryRowNormalized) {
      console.log(`✅ Found Category by normalized categoryid: "${originalValue}" -> row ID: ${categoryRowNormalized.id}`);
      return categoryRowNormalized;
    }

    console.warn(`❌ No Category found with categoryid: "${originalValue}"`);
    return null;

  } catch (error) {
    console.error(`❌ Error searching for Category:`, error);
    return null;
  }
}

// Helper function to find related row with flexible matching (for other entities)
async function findRelatedRowFlexible(
  entityId: string,
  tenantId: string | null,
  searchValue: string
): Promise<any> {
  const originalValue = searchValue.trim();
  const normalizedValue = normalizeText(searchValue);

  if (!originalValue) return null;

  console.log(`🔍 Searching for related row: entityId=${entityId}, original="${originalValue}", normalized="${normalizedValue}"`);

  try {
    // Get all rows for this entity and tenant
    const allRows = await db.row.findMany({
      where: {
        entityId: entityId,
        tenantId: tenantId,
      },
      include: {
        values: {
          include: {
            property: true,
          },
        },
      },
    });

    console.log(`Found ${allRows.length} rows to search through`);

    // Search through all rows and their values with flexible matching
    const foundValues = [];
    for (const row of allRows) {
      for (const value of row.values) {
        if (value.textValue) {
          const dbValue = value.textValue;
          const normalizedDbValue = normalizeText(dbValue);
          foundValues.push(dbValue);

          // Check for exact match (original values)
          if (dbValue === originalValue) {
            console.log(`✅ Found exact match: "${dbValue}" = "${originalValue}" -> row ID: ${row.id}`);
            return row;
          }

          // Check for normalized match (handles quotes, case, whitespace)
          if (normalizedDbValue === normalizedValue) {
            console.log(`✅ Found normalized match: "${dbValue}" -> "${originalValue}" -> row ID: ${row.id}`);
            return row;
          }
        }
      }
    }

    // Log some sample values for debugging
    console.log(`📋 Sample values found in database (first 10):`, foundValues.slice(0, 10));

    console.warn(`❌ No match found for: "${originalValue}" (normalized: "${normalizedValue}")`);
    return null;

  } catch (error) {
    console.error(`❌ Error searching for related row:`, error);
    return null;
  }
}



// Helper function to process relationship data
async function processRelationshipData(
  entity: EntityWithDetails,
  properties: { name: string; value: string }[],
  tenantId: string | null
): Promise<{
  regularProperties: { name: string; value: string }[];
  parentRows: { relationshipId: string; parentId: string }[];
  childRows: { relationshipId: string; childId: string }[];
}> {
  const regularProperties: { name: string; value: string }[] = [];
  const parentRows: { relationshipId: string; parentId: string }[] = [];
  const childRows: { relationshipId: string; childId: string }[] = [];

  console.log(`\n=== Processing relationship data for entity: ${entity.name} ===`);
  console.log(`Parent relationships: ${entity.parentEntities.length}`);
  console.log(`Child relationships: ${entity.childEntities.length}`);
  console.log(`Properties to process:`, properties.map(p => `${p.name}=${p.value}`));

  for (const property of properties) {
    const { name, value } = property;
    let isRelationshipFieldFound = false;

    console.log(`\nProcessing property: ${name}=${value}`);

    // Check parent relationships (many-to-one)
    for (const relationship of entity.parentEntities) {
      const relatedEntityName = relationship.parent.name;
      const fieldMatch = isRelationshipField(name, relatedEntityName);

      console.log(`Checking parent relationship: ${relatedEntityName}, fieldMatch:`, fieldMatch);

      if (fieldMatch.isMatch && fieldMatch.type) {
        console.log(`Found matching relationship field: ${name} -> ${relatedEntityName} (${fieldMatch.type})`);

        if (value && value.trim() && value.trim() !== 'undefined') {
          // Handle multiple values separated by :::
          const values = value.split(':::').map(v => v.trim()).filter(v => v);

          for (const singleValue of values) {
            const relatedRow = await findRelatedRowFlexible(
              relationship.parent.id,
              tenantId,
              singleValue
            );

            if (relatedRow) {
              parentRows.push({
                relationshipId: relationship.id,
                parentId: relatedRow.id,
              });
              console.log(`✅ Created parent relationship: ${name}=${singleValue} -> ${relatedEntityName} row ID: ${relatedRow.id}`);
            } else {
              console.warn(`❌ Could not find related ${relatedEntityName} with ${fieldMatch.type}: ${singleValue}`);
            }
          }
        }
        isRelationshipFieldFound = true;
        break;
      }
    }

    // Check child relationships (one-to-many) if not already processed
    if (!isRelationshipFieldFound) {
      for (const relationship of entity.childEntities) {
        const relatedEntityName = relationship.child.name;
        const fieldMatch = isRelationshipField(name, relatedEntityName);

        console.log(`Checking child relationship: ${relatedEntityName}, fieldMatch:`, fieldMatch);

        if (fieldMatch.isMatch && fieldMatch.type) {
          console.log(`Found matching child relationship field: ${name} -> ${relatedEntityName} (${fieldMatch.type})`);

          if (value && value.trim() && value.trim() !== 'undefined') {
            // Handle multiple values separated by :::
            const values = value.split(':::').map(v => v.trim()).filter(v => v);

            for (const singleValue of values) {
              const relatedRow = await findRelatedRowFlexible(
                relationship.child.id,
                tenantId,
                singleValue
              );

              if (relatedRow) {
                childRows.push({
                  relationshipId: relationship.id,
                  childId: relatedRow.id,
                });
                console.log(`✅ Created child relationship: ${name}=${singleValue} -> ${relatedEntityName} row ID: ${relatedRow.id}`);
              } else {
                console.warn(`❌ Could not find related ${relatedEntityName} with ${fieldMatch.type}: ${singleValue}`);
              }
            }
          }
          isRelationshipFieldFound = true;
          break;
        }
      }
    }

    // If not a relationship field, add to regular properties
    if (!isRelationshipFieldFound) {
      console.log(`Adding as regular property: ${name}=${value}`);
      regularProperties.push(property);
    }
  }

  console.log(`\n=== Relationship processing complete ===`);
  console.log(`Regular properties: ${regularProperties.length}`);
  console.log(`Parent relationships: ${parentRows.length}`);
  console.log(`Child relationships: ${childRows.length}`);
  console.log(`Parent relationship IDs: ${parentRows.map(p => p.parentId).join(', ')}`);
  console.log(`Child relationship IDs: ${childRows.map(c => c.childId).join(', ')}`);
  console.log(`=======================================\n`);

  return {
    regularProperties,
    parentRows,
    childRows,
  };
}

export namespace Rows_Import {
  export type LoaderData = {
    meta: MetaTagsDto;
    entity: EntityWithDetails;
    allTenants: { id: string; name: string; slug: string }[];
  };
  export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    const { t, tenantId, entity } = await RowsRequestUtils.getLoader({ request, params });
    if (!entity.isAutogenerated || entity.type === "system") {
      throw redirect(tenantId ? UrlUtils.currentTenantUrl(params, "404") : "/404");
    }
    const data: LoaderData = {
      meta: [{ title: `${t("shared.import")} ${t(entity.titlePlural)} | ${process.env.APP_NAME}` }],
      entity,
      allTenants: !tenantId ? await adminGetAllTenantsIdsAndNames() : [],
    };
    return Response.json(data);
  };

  export interface ImportRow {
    properties: { name: string; value: string }[];
    row?: RowWithDetails | null;
    error?: string;
  }
  export type ActionData = {
    rows?: ImportRow[];
    error?: string;
  };
  export const action: ActionFunction = async ({ request, params }) => {
    const { t, userId, tenantId, entity, form } = await RowsRequestUtils.getAction({ request, params });
    const action = form.get("action");
    if (action === "import") {
      const tag = form.get("tag")?.toString() ?? "import";
      const rawRows: ImportRow[] = form.getAll("rows[]").map((f: FormDataEntryValue) => {
        return JSON.parse(f.toString());
      });
      let tenantToImport = tenantId;
      if (tenantId === null) {
        const selectedTenantId = form.get("selectedTenantId")?.toString() || "{null}";
        if (selectedTenantId === "{null}") {
          tenantToImport = null;
        } else {
          const existingTenant = await getTenant(selectedTenantId);
          if (!existingTenant) {
            return Response.json({ error: "Invalid tenant with ID: " + selectedTenantId }, { status: 400 });
          }
          tenantToImport = selectedTenantId;
        }
      }

      if (rawRows.length === 0) {
        return Response.json({ error: "No rows to import" }, { status: 400 });
      }
      const rows: ImportRow[] = [];
      let folio = 1;
      const maxFolio = await getMaxRowFolio({ tenantId: tenantToImport, entityId: entity.id });
      if (maxFolio && maxFolio._max.folio !== null) {
        folio = maxFolio._max.folio + 1;
      }
      await Promise.all(
        rawRows.map(async (importRow: ImportRow, idx) => {
          try {
            // Wait for keeping folios unique, I can't think of another way to do this
            await new Promise((r) => setTimeout(r, 1500));

            // Process relationship data
            const { regularProperties, parentRows, childRows } = await processRelationshipData(
              entity,
              importRow.properties,
              tenantToImport
            );

            // Debug logging
            console.log(`Processing row ${idx + 1}:`, {
              originalProperties: importRow.properties.length,
              regularProperties: regularProperties.length,
              parentRows: parentRows.length,
              childRows: childRows.length,
              parentRowIds: parentRows.map(p => p.parentId),
              childRowIds: childRows.map(c => c.childId),
            });

            // Get row values from regular properties only
            const rowValues = RowHelper.getRowPropertiesFromForm({
              t,
              entity,
              values: regularProperties
            });

            // Add relationship data to row values
            if (parentRows.length > 0 || childRows.length > 0) {
              rowValues.parentRows = parentRows;
              rowValues.childRows = childRows;
            }

            const newRow = await RowsApi.create({
              entity,
              tenantId: tenantToImport,
              userId,
              rowValues,
              nextFolio: folio + idx,
              request,
            });
            if (tag) {
              await RowsApi.addTag({ row: newRow, tag: { value: tag, color: Colors.INDIGO } });
            }
            importRow.row = await getRowById(newRow.id);
          } catch (e: any) {
            importRow.error = e.message?.toString();
          }
          rows.push(importRow);
        })
      );
      const data: ActionData = {
        rows,
      };
      return data;
    } else {
      return Response.json({ error: "Invalid form" }, { status: 400 });
    }
  };
}
