import { useNavigation, useSubmit } from "react-router";
import { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import XIcon from "~/components/ui/icons/XIcon";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import { t } from "i18next";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
const AddTaskForm = ({ onClose, task, setTask }: { onClose: () => void; task: any, setTask: any }) => {
  const [taskTitle, setTaskTitle] = useState(task == null ? "" : task?.title);
  const [taskDescription, setTaskDescription] = useState(task == null ? "" : task?.description);
  const [priority, setPriority] = useState(task == null ? "" : task?.priority);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const navigation = useNavigation();
  const isAdding = navigation.state === "submitting" && navigation.formData?.get("action") === "task-new";
  const submit = useSubmit();
  const [error, setError] = useState("");
  function handleFormSubmit(e: any) {
    e.preventDefault();
    const form = new FormData();
    if (!priority) {
      setError("Priority is required.");
      return;
    }
    setError("");

    form.set("action", task == null ? "task-new" : "task-edit");
    if (task != null) form.set("task-id", task.id);
    form.set("task-title", taskTitle);
    form.set("task-description", taskDescription);
    form.set("priority", priority);

    submit(form, {
      method: "post",
    });

    if (task == null) {
      formRef.current?.reset();
      onClose();
    } else {
      setTask({ ...task, title: taskTitle, description: taskDescription, priority: priority });
      onClose();
    }
  }

  useEffect(() => {
    if (!isAdding) {
      formRef.current?.reset();
    }

  }, [isAdding]);

  return createPortal(

    <div className="fixed inset-0 z-50 flex justify-end bg-black/30 !bg-opacity-30">
      <div className="relative w-[374px] h-full translate-x-0 transform rounded-l-md bg-white shadow-lg transition-transform duration-300 ease-in-out flex flex-col">

        <div className="flex items-center justify-between border-b border-input p-4">
          <div className="flex items-center gap-2">
            <span className="text-foreground font-semibold text-sm">Add Task</span>
          </div>

          <div className="flex items-center space-x-4 border-l border-[#D5D5D5] pl-2.5">
            <>
              <button
                type="button"
                onClick={onClose}
                className="flex items-center justify-center rounded-md text-muted-foreground hover:text-foreground cursor-pointer"
              >
                <XIcon className="size-5" aria-hidden="true" />
              </button>
            </>
          </div>
        </div>


        <form
          ref={formRef}
          onSubmit={handleFormSubmit}
          className="flex-1 overflow-y-auto p-4"
        >
          <input hidden readOnly name="action" value="task-new" />
          <input hidden name="priority" value={priority} />

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">
              Task Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={taskTitle}
              name="task-title"
              onChange={(e) => setTaskTitle(e.target.value)}
              placeholder="Ex: Need hiring on urgent basis"
              className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">Task Description</label>
            <textarea
              value={taskDescription}
              name="task-description"
              onChange={(e) => setTaskDescription(e.target.value)}
              placeholder="Ex: Need to hire product designers for the new upcoming project with 3y experience."
              rows={3}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
            ></textarea>
          </div>

          <div className="relative mb-4">
            <label className="block text-sm font-medium text-gray-700">
              Priority <span className="text-red-500">*</span>
            </label>
            <div
              className="mt-1 flex cursor-pointer items-center justify-between rounded-md border border-gray-300 p-2"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <span className={priority ? "text-black" : "text-gray-400"}>
                {priority || "Select"}
              </span>
              <svg
                className={`h-4 w-4 transform transition-transform ${isDropdownOpen ? "rotate-180" : ""}`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>

            {isDropdownOpen && (
              <ul className="absolute z-10 mt-1 w-full rounded-md border border-gray-300 bg-white shadow-md">
                {["High", "Medium", "Low"].map((level) => (
                  <li
                    key={level}
                    className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                    onClick={() => {
                      setPriority(level);
                      setIsDropdownOpen(false);
                    }}
                  >
                    {level}
                  </li>
                ))}
              </ul>
            )}
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>
        </form>

        <div className="border-t border-input px-4 py-3 flex justify-end gap-4">
          {onClose && (
            <ButtonSecondary onClick={onClose} className="border border-[#E6E6E6] cursor-pointer">
              <div>{t("shared.cancel")}</div>
            </ButtonSecondary>
          )}
         
          {handleFormSubmit &&(
           <LoadingButton type="submit" onClick={handleFormSubmit} >
             Save Details                 
            </LoadingButton>
            )}

        </div>
      </div>
    </div>,
    document.body
  );
};

export default AddTaskForm;
