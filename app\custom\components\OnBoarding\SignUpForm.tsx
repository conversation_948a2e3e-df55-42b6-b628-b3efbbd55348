import { useState } from "react";
import { LeftSection } from "./LeftSection";
import { BackArrowIcon, BoxSvg, EyeClose, EyeOpen, SignUpIcon } from "./Svg";
import { Form, Link, useNavigation, useSearchParams } from "react-router";
import SuccessBanner from "~/components/ui/banners/SuccessBanner";
import { useTranslation } from "react-i18next";
import { useRootData } from "~/utils/data/useRootData";
import InputText from "~/components/ui/input/InputText";
import clsx from "~/utils/shared/ClassesUtils";
import LoadingButton from "~/components/ui/buttons/LoadingButton";

interface Props {
  requireRecaptcha?: boolean;
  isVerifyingEmail?: boolean;
  isSettingUpAccount?: boolean;
  data?: { company?: string; firstName?: string; lastName?: string; email?: string; slug?: string };
  error: string | undefined;
  actionData?: ActionData;
}
type ActionData = {
  error?: string;
  verificationEmailSent?: boolean;
};

export function SignUpForm({ requireRecaptcha = false, isVerifyingEmail = false, isSettingUpAccount = false, data = {}, error, actionData }: Props) {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const { t } = useTranslation();
  const { appConfiguration, csrf } = useRootData();
  const navigation = useNavigation();

  const [searchParams] = useSearchParams();
  const redirect = searchParams.get("redirect") ?? undefined;
  const showPasswordInput = !appConfiguration.auth.requireEmailVerification || isVerifyingEmail || isSettingUpAccount;

  return (
    <div className="flex w-full flex-col md:space-x-4 lg:flex-row">
      <div className="hidden lg:block lg:w-[60%]">
        <LeftSection />
      </div>

      {/* Right Section */}
      <div className="relative flex w-full flex-col lg:min-h-screen lg:w-[40%]">
        {/* Top Box SVG - Hidden on mobile */}
        <div className="">
          <BoxSvg className="rotate-180 lg:mt-2" />
        </div>

        {/* Form Container */}
        <div className="flex flex-1 flex-col items-center justify-center px-4 py-8 lg:px-6 lg:py-4 lg:pr-10">
          <div className="w-full max-w-[500px] space-y-7">
            {/* Header */}
            <div className="space-y-3">
              <SignUpIcon className="h-6 w-6 lg:h-auto lg:w-auto" />
              <h1 className="text-foreground text-2xl font-medium lg:text-[28px] lg:leading-[1.5]">
                Sign up <br />
                to linkworks
              </h1>
            </div>

            {/* Form Fields */}
            <Form method="post" className="flex flex-col gap-4 space-y-[18px]">
              <input type="hidden" name="csrf" value={csrf} hidden readOnly />

              {/* Tenant */}
              {appConfiguration.auth.requireOrganization && (
                <div>
                  <InputText
                    title={t("models.tenant.object")}
                    disabled={navigation.state !== "idle"}
                    autoFocus
                    type="text"
                    name="company"
                    id="company"
                    placeholder={t("account.shared.companyPlaceholder")}
                    required
                    defaultValue={data.company}
                    className="w-full"
                  />
                </div>
              )}

              {/* Email Field */}
              <div className="flex flex-col gap-1">
                <InputText
                  type="text"
                  title={t("account.shared.email")}
                  id="email"
                  name="email"
                  autoComplete="email"
                  required
                  readOnly={isVerifyingEmail}
                  defaultValue={data.email}
                  className={clsx(
                    "bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]",
                    isVerifyingEmail && "bg-muted-foreground cursor-not-allowed"
                  )}
                  placeholder="John@"
                  disabled={navigation.state !== "idle"}
                />
              </div>

              {/* Password Field */}
              {showPasswordInput && (
                <div className="flex flex-col gap-1">
                  <div className="relative">
                    <InputText
                      title={t("account.shared.password")}
                      type={passwordVisible ? "text" : "password"}
                      id="password"
                      name="password"
                      required
                      placeholder="*************"
                      className="bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                      disabled={navigation.state !== "idle"}
                      defaultValue=""
                    />
                    {/* <button
                      type="button"
                      onClick={() => setPasswordVisible(!passwordVisible)}
                      className="absolute top-11 right-3 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center lg:top-11 lg:right-3"
                    >
                      {!passwordVisible ? <EyeOpen /> : <EyeClose />}
                    </button> */}
                  </div>
                </div>
              )}

              {/* Confirm Password Field */}
              {showPasswordInput && (
                <div className="flex flex-col gap-1">
                  <div className="relative">
                    <InputText
                      title={t("account.shared.confirmPassword")}
                      type={confirmPasswordVisible ? "text" : "password"}
                      id="verifyPassword"
                      name="verifyPassword"
                      required
                      placeholder="*************"
                      className="bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                      disabled={navigation.state !== "idle"}
                      defaultValue=""
                    />
                    {/* <button
                      type="button"
                      onClick={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                      className="absolute top-11 right-3 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center lg:top-11 lg:right-3"
                    >
                      {!confirmPasswordVisible ? <EyeOpen /> : <EyeClose />}
                    </button> */}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="pt-[26px]">
                <LoadingButton
                  disabled={navigation.state !== "idle"}
                  className="bg-primary text-background hover:bg-primary flex h-10 w-full items-center justify-center rounded px-3 text-center text-sm font-bold transition-all duration-200 lg:h-8 lg:text-[12px]"
                  type="submit"
                >
                  {t("account.register.prompts.register.title")}
                </LoadingButton>
              </div>

              {/* Divider */}
              <div className="flex items-center justify-center gap-2.5">
                <div className="bg-input h-px w-[45px]" />
                <span className="text-foreground text-xs lg:text-[13px]">(Or)</span>
                <div className="bg-input h-px w-[45px]" />
              </div>

              {/* Back to Login Button */}
              <button type="button" className="flex h-10 w-full items-center justify-center gap-2 rounded px-3 lg:h-8">
                <BackArrowIcon />
                {!actionData?.verificationEmailSent ? (
                  <Link to="/login" className="text-primary text-sm lg:text-[12px]">
                    {t("account.register.clickHereToLogin")}
                  </Link>
                ) : (
                  <div className="mt-4">
                    <SuccessBanner title={t("shared.success")} text={t("account.verify.emailSent")} />
                  </div>
                )}
              </button>
            </Form>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="flex items-center justify-center px-4 pb-3 lg:-ml-40 lg:space-x-2 2xl:-ml-52">
          <BoxSvg className="hidden lg:block" />
          <div className="max-w-[366px] text-center text-xs leading-[17px] sm:text-[10px] lg:text-[10px]">
            {t("account.register.bySigningUp")}{" "}
            <Link target="_blank" to="/terms-and-conditions" className="text-primary underline">
              {t("account.register.termsAndConditions")}
            </Link>{" "}
            {t("account.register.andOur")}{" "}
            <Link target="_blank" to="/privacy-policy" className="text-primary underline">
              {t("account.register.privacyPolicy")}
            </Link>
            <span className="text-primary">.</span>
            <br />
            <span className="text-foreground">{t("account.register.termsAndCondition")}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
