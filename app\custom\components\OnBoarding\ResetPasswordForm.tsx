import { useEffect, useState } from "react";
import { LeftSection } from "./LeftSection";
import { BackArrowIcon, BoxSvg, EyeClose, EyeOpen, ResetIcon } from "./Svg";
import { useTranslation } from "react-i18next";
import { Form, Link, useLocation, useNavigation, useSearchParams } from "react-router";
import InputText from "~/components/ui/input/InputText";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
import ExclamationTriangleIcon from "~/components/ui/icons/ExclamationTriangleIcon";
import InfoBanner from "~/components/ui/banners/InfoBanner";

type LoaderData = {
  email: string;
};
type ActionData = {
  success?: string;
  error?: string;
  fields?: {
    email: string;
    verifyToken: string;
  };
};
interface Props {
  actionData?: ActionData;
  data?: LoaderData;
}

export function ResetPasswordForm({ actionData, data }: Props) {
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const { t } = useTranslation();
  const navigation = useNavigation();

  const search = useLocation().search;
  const [verifyToken] = useState(actionData?.fields?.email || new URLSearchParams(search).get("t") || "");

  const [, setSearchParams] = useSearchParams();
  const [actionResult, setActionResult] = useState<{ error?: string; success?: string }>();

  useEffect(() => {
    if (actionData) {
      setActionResult(actionData);
    }
    if (actionData?.error && actionData.fields) {
      setSearchParams({ e: actionData.fields.email, t: actionData.fields.verifyToken });
    }
  }, [actionData, setSearchParams]);

  return (
    <div className="flex w-full flex-col md:space-x-4 lg:flex-row">
      {/* Left Section - Hidden on mobile */}
      <div className="hidden lg:block lg:w-[60%]">
        <LeftSection />
      </div>

      {/* Right Section */}
      <div className="flex min-h-screen w-full flex-col justify-between py-6 lg:w-[40%] lg:py-8">
        <BoxSvg className="rotate-180" />

        {/* Form Container */}
        <div className="flex flex-1 items-center justify-center px-4 lg:px-10">
          <div className="flex w-full max-w-md flex-col gap-6 lg:max-w-[500px] lg:gap-7">
            {/* Header */}
            <div className="flex w-full flex-col gap-3">
              <ResetIcon className="h-6 w-6" />
              <h1 className="text-foreground text-2xl font-medium lg:text-[28px] lg:leading-[1.5]">Reset Password</h1>
            </div>

            {/* Form */}
            <Form method="post" className="flex w-full flex-col gap-4 lg:gap-[18px]">
              <input type="hidden" name="verify-token" defaultValue={verifyToken} required hidden readOnly />

              {/* New Password Field */}
              <div className="flex flex-col gap-1">
                <div className="relative">
                  <InputText
                    title={t("account.shared.newPassword")}
                    autoFocus
                    id="newPassword"
                    name="newPassword"
                    type={newPasswordVisible ? "text" : "password"}
                    className="text-foreground bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                    placeholder="************"
                    required
                    defaultValue=""
                    minLength={2}
                  />
                  {/* <button
                    type="button"
                    onClick={() => setNewPasswordVisible(!newPasswordVisible)}
                    className="absolute top-11 right-3 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center lg:top-11 lg:right-3"
                  >
                    {!newPasswordVisible ? <EyeOpen /> : <EyeClose />}
                  </button> */}
                </div>
              </div>

              {/* Confirm Password Field */}
              <div className="flex flex-col gap-1">
                <div className="relative">
                  <InputText
                    title={t("account.shared.confirmPassword")}
                    id="confirmPassword"
                    name="confirmPassword"
                    type={confirmPasswordVisible ? "text" : "password"}
                    className="text-foreground bg-background text-gray-1 placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                    placeholder="************"
                    required
                    defaultValue=""
                    minLength={2}
                  />
                  {/* <button
                    type="button"
                    onClick={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                    className="absolute top-11 right-3 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center lg:top-11 lg:right-3"
                  >
                    {!confirmPasswordVisible ? <EyeOpen /> : <EyeClose />}
                  </button> */}
                </div>
              </div>

              {/* Error Message */}
              <div id="form-error-message">
                {actionResult?.error && navigation.state === "idle" ? (
                  <div className="text-destructive flex items-center justify-center space-x-2 text-sm" role="alert">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                    <div>{actionResult.error}</div>
                  </div>
                ) : null}
              </div>

              {/* Submit Button */}
              <div className="pt-4 lg:pt-[26px]">
                <LoadingButton
                  className="bg-primary text-background hover:bg-primary flex h-10 w-full items-center justify-center rounded px-3 text-sm font-bold transition-all duration-200 lg:h-8 lg:text-xs"
                  type="submit"
                >
                  {t("account.newPassword.continueButton")}
                </LoadingButton>
              </div>

              {actionResult?.success && (
                <InfoBanner title={t("account.reset.resetSuccess")} text={""}>
                  <Link to="/login" className="text-primary font-medium hover:underline">
                    {actionResult.success}
                  </Link>
                </InfoBanner>
              )}

              {/* Divider */}
              <div className="mt-4 flex items-center justify-center gap-2 lg:gap-[10px]">
                <div className="bg-input h-px w-10 lg:w-[45px]" />
                <span className="text-foreground text-xs lg:text-[13px]">(Or)</span>
                <div className="bg-input h-px w-10 lg:w-[45px]" />
              </div>

              {/* Back Button */}
              <button
                type="button"
                className="bg-background hover:border-primary mt-3 flex h-10 w-full items-center justify-center gap-2 rounded px-3 transition-all duration-200 lg:mt-4 lg:h-8"
              >
                <BackArrowIcon className="h-3 w-3" />
                <Link to="/login" className="text-primary text-sm lg:text-xs">
                  {t("account.register.clickHereToLogin")}
                </Link>
              </button>
            </Form>
          </div>
        </div>
        <BoxSvg className="-ml-24 rotate-180" />
      </div>
    </div>
  );
}
