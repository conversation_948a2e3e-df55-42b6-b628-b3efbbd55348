import { useEffect, useRef, useState } from "react";
import { LeftSection } from "./LeftSection";
import { BoxSvg, EyeClose, EyeOpen, MicroSoftIcon, SignInIcon } from "./Svg";
import { Form, Link, useNavigation, useSearchParams } from "react-router";
import { LoginActionData } from "~/modules/users/components/LoginForm";
import { useTranslation } from "react-i18next";
import { useRootData } from "~/utils/data/useRootData";
import InputText, { RefInputText } from "~/components/ui/input/InputText";
import GoogleIcon from "~/components/ui/icons/GoogleIcon";
import GitHubIcon from "~/components/ui/icons/GitHubIcon";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
import ExclamationTriangleIcon from "~/components/ui/icons/ExclamationTriangleIcon";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import InfoBanner from "~/components/ui/banners/InfoBanner";

type LoaderData = {
  metatags: MetaTagsDto;
  demoCredentials?: { email: string; password: string };
};

export function SignInForm({ actionData, redirectTo, data }: { actionData: LoginActionData | null; redirectTo?: string; data: LoaderData }) {
  const { t } = useTranslation();
  const { appConfiguration } = useRootData();
  const navigation = useNavigation();
  const [searchParams] = useSearchParams();
  const [showPassword, setShowPassword] = useState(false);

  const emailInput = useRef<RefInputText>(null);
  useEffect(() => {
    if (appConfiguration.auth.authMethods.emailPassword.enabled) {
      setTimeout(() => {
        emailInput.current?.input.current?.focus();
      }, 300);
    }
  }, [appConfiguration.auth.authMethods.emailPassword.enabled]);

  return (
    <div className="flex w-full flex-col md:space-x-4 lg:flex-row">
      {/* Left Section - Hidden on mobile */}
      <div className="hidden lg:block lg:w-[60%]">
        <LeftSection />
      </div>

      {/* Right Section */}
      <div className="flex min-h-screen w-full flex-col justify-between py-6 lg:w-[40%] lg:py-5">
        <div className="flex justify-between p-2">
          <BoxSvg className="rotate-180" />
          <div className="flex items-center">
            <p className="px-1 text-xs font-medium tracking-[0.01em] lg:text-xs lg:leading-[21px]">New Here?</p>
            <Link
              to={appConfiguration.subscription.allowSignUpBeforeSubscribe ? "/register" : "/pricing"}
              className="text-primary text-xs font-bold tracking-[0.01em] underline decoration-solid decoration-[0px] lg:text-xs lg:leading-[21px]"
            >
              {t("account.login.orRegister")}
            </Link>
          </div>
        </div>

        {/* Form Container */}
        <div className="flex flex-1 items-center justify-center px-4 lg:px-0">
          <div className="flex w-full max-w-[365px] flex-col gap-6 lg:max-w-[500px] lg:gap-7">
            {/* Header and New Here link */}
            <div className="flex w-full items-center justify-between">
              <div className="flex flex-col gap-3">
                <SignInIcon className="h-6 w-6" />
                <h1 className="text-foreground text-2xl font-medium lg:text-[28px] lg:leading-[100%]">
                  Sign in <br /> to continue
                </h1>
              </div>
            </div>

            <Form method="post" className="flex w-full flex-col items-center gap-4 lg:min-h-[207px] lg:gap-6.5">
              <input type="hidden" name="action" value="login" hidden readOnly />
              <input type="hidden" name="redirectTo" value={redirectTo ?? searchParams.get("redirect") ?? undefined} />

              {/* Error Message */}
              <div id="form-error-message">
                {actionData?.error && navigation.state === "idle" ? (
                  <div className="text-destructive flex items-center justify-center space-x-2 text-sm" role="alert">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                    <div>{actionData.error}</div>
                  </div>
                ) : null}
              </div>

              {/* Email Field */}
              <div className="flex w-full flex-col items-start gap-1">
                <InputText
                  ref={emailInput}
                  title={t("account.shared.email")}
                  autoFocus
                  id="email"
                  name="email"
                  type="email"
                  required
                  className="placeholder:text-muted-foreground/45 focus:ring-foreground my-2 h-10 w-full rounded-[4px] text-sm focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                  placeholder="John@"
                  disabled={navigation.state !== "idle"}
                  defaultValue={actionData?.fields?.email}
                />
                {actionData?.fieldErrors?.email ? (
                  <p className="text-destructive-foreground py-1 text-xs lg:py-2" role="alert" id="email-error">
                    {actionData.fieldErrors.email}
                  </p>
                ) : null}
              </div>

              {/* Password Field */}
              <div className="flex w-full flex-col items-start gap-1">
                <div className="relative w-full">
                  <InputText
                    id="password"
                    title={t("account.shared.password")}
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    className="placeholder:text-muted-foreground/45 focus:ring-foreground mt-2 h-10 w-full rounded-[4px] border border-none text-sm focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                    placeholder="*************"
                    disabled={navigation.state !== "idle"}
                    defaultValue={actionData?.fields?.password}
                  />
                  {/* <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute top-[50px] right-3 flex h-5 w-5 -translate-y-1/2 transform cursor-pointer items-center justify-center"
                  >
                    {!showPassword ? <EyeOpen className="h-4 w-4 text-muted-foreground" /> : <EyeClose className="h-5 w-5 text-muted-foreground" />}
                  </button> */}
                </div>
                {actionData?.fieldErrors?.password ? (
                  <p className="text-destructive-foreground py-1 text-xs lg:py-2" role="alert" id="password-error">
                    {actionData.fieldErrors.password}
                  </p>
                ) : null}
              </div>

              {/* Login Button */}
              <div className="bg-primary mt-6 w-full rounded-[4px] lg:mt-10">
                <LoadingButton
                  disabled={navigation.state !== "idle"}
                  className="bg-primary text-background hover:bg-primary flex h-10 w-full items-center justify-center text-sm font-bold transition-all duration-200 lg:h-8 lg:text-xs"
                  type="submit"
                  actionName="login"
                >
                  {t("account.login.button")}
                </LoadingButton>
              </div>

              {/* Or Divider */}
              <div className="mt-4 flex h-5 items-center justify-center gap-2.5 lg:mt-4.5 lg:h-[21px]">
                <div className="bg-input h-px w-10 lg:w-[45px]" />
                <span className="text-foreground text-xs lg:text-[13px] lg:leading-[21px]">(Or)</span>
                <div className="bg-input h-px w-10 lg:w-[45px]" />
              </div>

              {/* Social Login Options */}
              <div className="mt-3 flex w-full flex-col gap-3 lg:mt-4">
                {appConfiguration.auth.authMethods.github.enabled && (
                  <Link
                    className="bg-foreground text-background inline-flex h-10 w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors lg:h-8"
                    to={appConfiguration.auth.authMethods.github.authorizationURL}
                  >
                    <GitHubIcon className="text-background mr-2 h-4 w-4" /> {t("auth.github.button")}
                  </Link>
                )}
                {appConfiguration.auth.authMethods.google.enabled && (
                  <Form action="/oauth/google" method="post">
                    <button
                      type="submit"
                      className="bg-primary text-background hover:bg-primary inline-flex h-10 w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors lg:h-8"
                    >
                      <GoogleIcon className="text-background mr-2 h-4 w-4" /> {t("auth.google.button")}
                    </button>
                  </Form>
                )}
                {appConfiguration.auth.authMethods.azure.enabled && (
                  <Link
                    className="border-muted-foreground/25 bg-background inline-flex h-10 w-full items-center justify-center gap-2 rounded border px-1 shadow-[0px_2px_0px_0px_rgba(0,0,0,0.02)] lg:h-8"
                    to={appConfiguration.auth.authMethods.azure.authorizationURL}
                  >
                    <MicroSoftIcon />
                    <span className="text-xs font-normal lg:leading-[22px]">{t("auth.azure.button")}</span>
                  </Link>
                )}
              </div>
            </Form>

            {/* Demo Credentials Banner */}
            {data.demoCredentials && (
              <div className="mt-6 lg:mt-8">
                <InfoBanner title="Guest Demo Account" text="">
                  <b>email:</b>
                  <span className="select-all">{data.demoCredentials.email}</span>, <b>password:</b>
                  <span className="select-all">{data.demoCredentials.password}</span>.
                </InfoBanner>
              </div>
            )}
          </div>
        </div>

        <BoxSvg className="rotate-180 lg:-ml-22" />
      </div>
    </div>
  );
}
